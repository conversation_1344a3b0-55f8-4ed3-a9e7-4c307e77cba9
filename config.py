"""
Configuration file for Trading Analyzer Bot
"""

# Default settings
DEFAULT_TIMEFRAME = "15"  # 15 minutes
DEFAULT_THEME = "dark"    # Chart theme
DEFAULT_WIDTH = 1200      # Chart width
DEFAULT_HEIGHT = 800      # Chart height

# OpenAI settings
OPENAI_MODEL = "gpt-4o"   # Best model for vision analysis
OPENAI_MAX_TOKENS = 1500
OPENAI_TEMPERATURE = 0.3  # Lower temperature for more consistent analysis

# Chart-img API settings
CHART_IMG_TIMEOUT = 30    # Timeout for chart generation
IMAGE_DOWNLOAD_TIMEOUT = 30  # Timeout for image download

# Popular crypto symbols
POPULAR_CRYPTO_SYMBOLS = [
    "BINANCE:BTCUSDT",
    "BINANCE:ETHUSDT", 
    "BINANCE:ADAUSDT",
    "BINANCE:SOLUSDT",
    "BINANCE:DOTUSDT",
    "BINANCE:LINKUSDT",
    "BINANCE:AVAXUSDT",
    "BINANCE:MATICUSDT",
    "BINANCE:ATOMUSDT",
    "BINANCE:NEARUSDT",
    "BINANCE:FTMUSDT",
    "BINANCE:SANDUSDT",
    "BINANCE:MANAUSDT",
    "BINANCE:AXSUSDT",
    "BINANCE:CHZUSDT"
]

# Technical indicators for chart analysis
CHART_STUDIES = [
    {"name": "Bollinger Bands"},
    {"name": "Volume"},
    {"name": "Relative Strength Index"},
    {"name": "Moving Average Exponential", "inputs": {"length": 21}},
    {"name": "Moving Average Exponential", "inputs": {"length": 50}},
    {"name": "MACD"}
]

# Analysis prompt template
SYSTEM_PROMPT_TEMPLATE = """
Anda adalah "Futures Maximizer: UltraScalp" - spesialis analisa chart crypto pada timeframe 15 menit.

INSTRUKSI ANALISA:

1. VALIDASI TIMEFRAME:
   - Periksa apakah chart yang dikirim adalah timeframe 15 menit
   - Jika bukan 15M, respon: "Chart ini bukan 15 menit, silakan kirim chart dengan timeframe 15M agar bisa dianalisa."

2. ANALISA CHART (FORMAT TETAP):
   
   **Current Trend**: [Identifikasi tren: bullish/bearish/sideways]
   
   **Resistance & Support**: [Level resistance dan support terdekat secara visual]
   
   **Entry Futures Setup**:
   - Entry: [Harga masuk posisi]
   - Stop Loss: [Titik cut-loss]
   - Take Profit: [Target ambil untung]
   - Posisi: [HANYA Long ATAU Short, tidak boleh dua-duanya]
   - RiskReward: [Rasio 1:2 hingga 1:4]
   
   **Estimasi Waktu Potensi Gerakan**: [Durasi setup bisa berjalan, misal: 1-3 jam]
   
   **Skenario Lanjutan**:
   - If TP: [Jika Take Profit tercapai, setup lanjutan]
   - If SL: [Jika Stop Loss tersentuh, setup cadangan]

3. GAYA BAHASA:
   - Profesional dan mudah dimengerti
   - Langsung ke poin, tanpa bertele-tele
   - Hindari bahasa teknikal berlebihan

LARANGAN:
- Jangan berikan dua opsi setara (long DAN short)
- Jangan analisa chart selain timeframe 15M
- Jangan jawab tanpa mengikuti struktur analisa
"""

# File naming patterns
ANALYSIS_FILE_PATTERN = "analysis_{symbol}_{timeframe}M_{timestamp}.txt"
LOG_FILE_PATTERN = "bot_log_{date}.txt"

# Error messages
ERROR_MESSAGES = {
    "chart_generation_failed": "Error: Failed to generate chart",
    "image_download_failed": "Error: Failed to download chart image", 
    "openai_analysis_failed": "Error: Failed to analyze chart with OpenAI",
    "invalid_api_key": "Error: Invalid API key",
    "network_error": "Error: Network connection failed",
    "timeout_error": "Error: Request timeout"
}

# Success messages
SUCCESS_MESSAGES = {
    "chart_generated": "Chart generated successfully",
    "image_downloaded": "Chart image downloaded successfully",
    "analysis_completed": "Analysis completed successfully",
    "file_saved": "Analysis saved to file successfully"
}

# Supported exchanges and their prefixes
SUPPORTED_EXCHANGES = {
    "BINANCE": "Binance",
    "COINBASE": "Coinbase Pro", 
    "KRAKEN": "Kraken",
    "BYBIT": "Bybit",
    "OKX": "OKX",
    "KUCOIN": "KuCoin"
}

# Common trading pairs suffixes
TRADING_PAIR_SUFFIXES = [
    "USDT", "BUSD", "USD", "BTC", "ETH", "BNB"
]
