#!/usr/bin/env python3
"""
Scheduled Trading Analyzer - Run analysis at scheduled intervals
"""

import time
import schedule
import threading
from datetime import datetime, timedelta
from typing import List, Optional
from trading_analyzer_secure import TradingAnalyzerBot
from batch_analyzer import BatchTradingAnalyzer
import config

class ScheduledTradingAnalyzer:
    def __init__(self, openai_api_key: str = None, chart_img_api_key: str = None):
        """
        Initialize Scheduled Trading Analyzer
        
        Args:
            openai_api_key: OpenAI API key (optional, will use env var if not provided)
            chart_img_api_key: Chart-img.com API key (optional, will use env var if not provided)
        """
        self.bot = TradingAnalyzerBot(openai_api_key, chart_img_api_key)
        self.batch_analyzer = BatchTradingAnalyzer(openai_api_key, chart_img_api_key)
        self.is_running = False
        self.analysis_count = 0
        
    def analyze_watchlist(self, symbols: List[str], timeframe: str = None):
        """
        Analyze a watchlist of symbols
        
        Args:
            symbols: List of symbols to analyze
            timeframe: Chart timeframe
        """
        timeframe = timeframe or config.DEFAULT_TIMEFRAME
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"\n🕐 Scheduled Analysis Started at {timestamp}")
        print(f"📊 Analyzing {len(symbols)} symbols on {timeframe}M timeframe")
        print("=" * 60)
        
        try:
            # Run batch analysis
            results = self.batch_analyzer.analyze_multiple_symbols(
                symbols, 
                timeframe=timeframe, 
                delay=3  # Shorter delay for scheduled runs
            )
            
            # Save batch report
            report_file = self.batch_analyzer.save_batch_report(symbols, timeframe)
            
            # Print summary
            self.batch_analyzer.print_summary()
            
            self.analysis_count += 1
            print(f"\n✅ Scheduled analysis #{self.analysis_count} completed")
            print(f"📁 Report saved: {report_file}")
            
        except Exception as e:
            print(f"❌ Scheduled analysis failed: {str(e)}")
    
    def start_scheduler(self, symbols: List[str], interval_hours: int = 4, timeframe: str = None):
        """
        Start the scheduled analyzer
        
        Args:
            symbols: List of symbols to analyze
            interval_hours: Hours between analyses
            timeframe: Chart timeframe
        """
        timeframe = timeframe or config.DEFAULT_TIMEFRAME
        
        print(f"🚀 Starting Scheduled Trading Analyzer")
        print(f"📊 Symbols: {len(symbols)} crypto pairs")
        print(f"⏰ Interval: Every {interval_hours} hours")
        print(f"📈 Timeframe: {timeframe} minutes")
        print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # Schedule the analysis
        schedule.every(interval_hours).hours.do(
            self.analyze_watchlist, 
            symbols=symbols, 
            timeframe=timeframe
        )
        
        # Run first analysis immediately
        print("🔄 Running initial analysis...")
        self.analyze_watchlist(symbols, timeframe)
        
        # Start scheduler loop
        self.is_running = True
        print(f"\n⏰ Scheduler started. Next analysis in {interval_hours} hours.")
        print("Press Ctrl+C to stop the scheduler.")
        
        try:
            while self.is_running:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
                
        except KeyboardInterrupt:
            print("\n⏹️ Scheduler stopped by user.")
            self.is_running = False
    
    def stop_scheduler(self):
        """Stop the scheduler"""
        self.is_running = False
        schedule.clear()
        print("⏹️ Scheduler stopped.")

def create_watchlist() -> List[str]:
    """Create a custom watchlist"""
    print("\n📝 Create Your Watchlist")
    print("=" * 30)
    print("Available options:")
    print("1. Use popular crypto symbols")
    print("2. Create custom watchlist")
    print("3. Use top 10 symbols")
    print("4. Use top 5 symbols")
    
    choice = input("\nSelect option (1-4): ").strip()
    
    if choice == "1":
        return config.POPULAR_CRYPTO_SYMBOLS
    elif choice == "2":
        print("\nEnter symbols separated by commas:")
        print("Example: BINANCE:BTCUSDT,BINANCE:ETHUSDT,BINANCE:ADAUSDT")
        symbols_input = input("Symbols: ").strip()
        symbols = [s.strip() for s in symbols_input.split(",") if s.strip()]
        return symbols if symbols else config.POPULAR_CRYPTO_SYMBOLS[:5]
    elif choice == "3":
        return config.POPULAR_CRYPTO_SYMBOLS[:10]
    elif choice == "4":
        return config.POPULAR_CRYPTO_SYMBOLS[:5]
    else:
        print("Invalid choice. Using top 5 symbols.")
        return config.POPULAR_CRYPTO_SYMBOLS[:5]

def main():
    """Main function for scheduled analyzer"""
    try:
        print("🕐 Scheduled Trading Analyzer")
        print("=" * 50)
        
        # Initialize analyzer
        analyzer = ScheduledTradingAnalyzer()
        
        # Create watchlist
        symbols = create_watchlist()
        print(f"\n📊 Watchlist created with {len(symbols)} symbols:")
        for i, symbol in enumerate(symbols, 1):
            print(f"  {i}. {symbol}")
        
        # Get interval
        print(f"\n⏰ Schedule Settings")
        print("=" * 20)
        interval_input = input("Analysis interval in hours (default: 4): ").strip()
        interval_hours = int(interval_input) if interval_input.isdigit() else 4
        
        # Get timeframe
        timeframe_input = input("Chart timeframe in minutes (default: 15): ").strip()
        timeframe = timeframe_input if timeframe_input.isdigit() else "15"
        
        # Confirm settings
        print(f"\n📋 Configuration Summary")
        print("=" * 30)
        print(f"Symbols: {len(symbols)} crypto pairs")
        print(f"Interval: Every {interval_hours} hours")
        print(f"Timeframe: {timeframe} minutes")
        print(f"Next analysis: Immediately, then every {interval_hours} hours")
        
        confirm = input("\nStart scheduled analyzer? (y/n): ").strip().lower()
        
        if confirm in ['y', 'yes']:
            # Start scheduler
            analyzer.start_scheduler(symbols, interval_hours, timeframe)
        else:
            print("Scheduled analyzer cancelled.")
            
    except KeyboardInterrupt:
        print("\n⏹️ Scheduled analyzer stopped by user.")
    except Exception as e:
        print(f"\n❌ Error in scheduled analyzer: {str(e)}")

def run_one_time_analysis():
    """Run a one-time scheduled-style analysis"""
    try:
        print("🔄 One-Time Batch Analysis")
        print("=" * 40)
        
        # Initialize analyzer
        analyzer = ScheduledTradingAnalyzer()
        
        # Create watchlist
        symbols = create_watchlist()
        
        # Get timeframe
        timeframe_input = input("\nChart timeframe in minutes (default: 15): ").strip()
        timeframe = timeframe_input if timeframe_input.isdigit() else "15"
        
        # Run analysis
        print(f"\n🚀 Starting one-time analysis...")
        analyzer.analyze_watchlist(symbols, timeframe)
        
        print(f"\n✅ One-time analysis completed!")
        
    except Exception as e:
        print(f"\n❌ Error in one-time analysis: {str(e)}")

if __name__ == "__main__":
    print("🕐 Scheduled Trading Analyzer")
    print("=" * 50)
    print("Options:")
    print("1. Start scheduled analyzer (continuous)")
    print("2. Run one-time batch analysis")
    print("3. Exit")
    
    choice = input("\nSelect option (1-3): ").strip()
    
    if choice == "1":
        main()
    elif choice == "2":
        run_one_time_analysis()
    elif choice == "3":
        print("Goodbye!")
    else:
        print("Invalid choice. Exiting.")
