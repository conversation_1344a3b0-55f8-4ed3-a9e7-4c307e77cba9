#!/usr/bin/env python3
"""
Demo script untuk testing Trading Analyzer Bot
"""

import sys
import os
from trading_analyzer_bot import TradingAnalyzerBot

def test_chart_generation():
    """Test chart generation functionality"""
    print("🧪 Testing Chart Generation...")
    
    # API Keys
    OPENAI_API_KEY = "***************************************************"
    CHART_IMG_API_KEY = "of8uldEeWa2bVpOuhdqAI90JzsfcIYCP8BJSgMsw"
    
    try:
        bot = TradingAnalyzerBot(OPENAI_API_KEY, CHART_IMG_API_KEY)
        
        # Test chart generation
        test_symbol = "BINANCE:BTCUSDT"
        print(f"Testing chart generation for {test_symbol}...")
        
        chart_url = bot.generate_chart(test_symbol, "15", "dark")
        
        if chart_url:
            print(f"✅ Chart generation successful!")
            print(f"Chart URL: {chart_url}")
            
            # Test image download
            print("Testing image download...")
            image_bytes = bot.download_chart_image(chart_url)
            
            if image_bytes:
                print(f"✅ Image download successful! Size: {len(image_bytes)} bytes")
                
                # Test base64 encoding
                print("Testing base64 encoding...")
                image_base64 = bot.encode_image_to_base64(image_bytes)
                print(f"✅ Base64 encoding successful! Length: {len(image_base64)} characters")
                
                return True
            else:
                print("❌ Image download failed")
                return False
        else:
            print("❌ Chart generation failed")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        return False

def test_full_analysis():
    """Test full analysis workflow"""
    print("\n🧪 Testing Full Analysis Workflow...")
    
    # API Keys
    OPENAI_API_KEY = "***************************************************"
    CHART_IMG_API_KEY = "of8uldEeWa2bVpOuhdqAI90JzsfcIYCP8BJSgMsw"
    
    try:
        bot = TradingAnalyzerBot(OPENAI_API_KEY, CHART_IMG_API_KEY)
        
        # Test symbols
        test_symbols = [
            "BINANCE:BTCUSDT",
            "BINANCE:ETHUSDT"
        ]
        
        for symbol in test_symbols:
            print(f"\nTesting analysis for {symbol}...")
            
            result = bot.analyze_symbol(symbol, "15")
            
            if result and not result.startswith("Error:"):
                print(f"✅ Analysis successful for {symbol}")
                print("Analysis preview:")
                print("-" * 40)
                # Show first 200 characters of analysis
                preview = result[:200] + "..." if len(result) > 200 else result
                print(preview)
                print("-" * 40)
            else:
                print(f"❌ Analysis failed for {symbol}")
                if result:
                    print(f"Error: {result}")
                return False
                
        return True
        
    except Exception as e:
        print(f"❌ Full analysis test failed with error: {str(e)}")
        return False

def main():
    """Main demo function"""
    print("🚀 Trading Analyzer Bot - Demo Test")
    print("=" * 50)
    
    # Test 1: Chart Generation
    chart_test_passed = test_chart_generation()
    
    if not chart_test_passed:
        print("\n❌ Chart generation test failed. Stopping demo.")
        return
    
    # Ask user if they want to continue with full analysis test
    print("\n" + "=" * 50)
    user_input = input("Chart generation test passed! Continue with full analysis test? (y/n): ").strip().lower()
    
    if user_input in ['y', 'yes']:
        # Test 2: Full Analysis
        analysis_test_passed = test_full_analysis()
        
        if analysis_test_passed:
            print("\n✅ All tests passed! Bot is working correctly.")
            print("\nYou can now run the main bot with:")
            print("python trading_analyzer_bot.py")
            print("or")
            print("python trading_analyzer_secure.py")
        else:
            print("\n❌ Analysis test failed. Please check your OpenAI API key and credits.")
    else:
        print("\nDemo stopped by user.")
    
    print("\n" + "=" * 50)
    print("Demo completed.")

if __name__ == "__main__":
    main()
