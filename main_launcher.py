#!/usr/bin/env python3
"""
Main Launcher for Trading Analyzer Bot
Provides access to all bot features through a unified interface
"""

import os
import sys
from datetime import datetime
import config

def print_banner():
    """Print application banner"""
    print("🚀" + "=" * 58 + "🚀")
    print("🚀" + " " * 58 + "🚀")
    print("🚀" + "     TRADING ANALYZER BOT - FUTURES MAXIMIZER     ".center(58) + "🚀")
    print("🚀" + "        UltraScalp 15-Min Timeframe Specialist        ".center(58) + "🚀")
    print("🚀" + " " * 58 + "🚀")
    print("🚀" + "=" * 58 + "🚀")
    print()
    print("📊 Crypto Futures Analysis with AI-Powered Chart Reading")
    print("🤖 Powered by OpenAI GPT-4o + TradingView Charts")
    print("⚡ Real-time Analysis • Risk/Reward Optimization • Entry Signals")
    print()

def print_main_menu():
    """Print main menu options"""
    print("📋 MAIN MENU")
    print("=" * 40)
    print("1. 🔍 Single Symbol Analysis")
    print("2. 📊 Batch Analysis (Multiple Symbols)")
    print("3. 🕐 Scheduled Analysis (Continuous)")
    print("4. 🧪 Demo & Testing")
    print("5. ⚙️  Configuration & Settings")
    print("6. 📚 Help & Documentation")
    print("7. 🚪 Exit")
    print("=" * 40)

def run_single_analysis():
    """Run single symbol analysis"""
    try:
        from trading_analyzer_secure import TradingAnalyzerBot
        
        print("\n🔍 SINGLE SYMBOL ANALYSIS")
        print("=" * 40)
        
        # Initialize bot
        bot = TradingAnalyzerBot()
        
        # Show popular symbols
        print("\n📊 Popular Crypto Symbols:")
        for i, symbol in enumerate(config.POPULAR_CRYPTO_SYMBOLS[:10], 1):
            print(f"  {i:2d}. {symbol}")
        
        print(f"\n💡 Or enter any symbol in format: EXCHANGE:SYMBOL")
        print(f"   Examples: BINANCE:BTCUSDT, COINBASE:BTC-USD")
        
        # Get symbol from user
        symbol_input = input("\nEnter symbol or number (1-10): ").strip()
        
        # Parse input
        if symbol_input.isdigit() and 1 <= int(symbol_input) <= 10:
            symbol = config.POPULAR_CRYPTO_SYMBOLS[int(symbol_input) - 1]
        else:
            symbol = symbol_input.upper()
        
        if not symbol:
            print("❌ No symbol entered. Returning to main menu.")
            return
        
        # Get timeframe
        timeframe_input = input(f"Timeframe in minutes (default: {config.DEFAULT_TIMEFRAME}): ").strip()
        timeframe = timeframe_input if timeframe_input.isdigit() else config.DEFAULT_TIMEFRAME
        
        print(f"\n🚀 Analyzing {symbol} on {timeframe}M timeframe...")
        
        # Run analysis
        result = bot.analyze_symbol(symbol, timeframe)
        
        if result and not result.startswith("Error:"):
            print(f"\n{'='*60}")
            print("📈 ANALYSIS RESULT")
            print(f"{'='*60}")
            print(result)
            print(f"{'='*60}")
            
            # Ask to save
            save_choice = input("\n💾 Save analysis to file? (y/n): ").strip().lower()
            if save_choice in ['y', 'yes']:
                bot.save_analysis_to_file(symbol, result, timeframe)
                print("✅ Analysis saved successfully!")
        else:
            print(f"❌ Analysis failed: {result}")
        
        input("\nPress Enter to continue...")
        
    except Exception as e:
        print(f"❌ Error in single analysis: {str(e)}")
        input("Press Enter to continue...")

def run_batch_analysis():
    """Run batch analysis"""
    try:
        from batch_analyzer import main as batch_main
        print("\n📊 BATCH ANALYSIS")
        print("=" * 40)
        batch_main()
        
    except Exception as e:
        print(f"❌ Error in batch analysis: {str(e)}")
        input("Press Enter to continue...")

def run_scheduled_analysis():
    """Run scheduled analysis"""
    try:
        from scheduled_analyzer import main as scheduled_main
        print("\n🕐 SCHEDULED ANALYSIS")
        print("=" * 40)
        scheduled_main()
        
    except Exception as e:
        print(f"❌ Error in scheduled analysis: {str(e)}")
        input("Press Enter to continue...")

def run_demo_testing():
    """Run demo and testing"""
    try:
        print("\n🧪 DEMO & TESTING")
        print("=" * 40)
        print("1. Run basic functionality test")
        print("2. Test chart generation only")
        print("3. Test OpenAI analysis only")
        print("4. Run full demo")
        print("5. Back to main menu")
        
        choice = input("\nSelect option (1-5): ").strip()
        
        if choice == "1":
            from demo_test import test_chart_generation
            test_chart_generation()
        elif choice == "2":
            from trading_analyzer_secure import TradingAnalyzerBot
            bot = TradingAnalyzerBot()
            chart_url = bot.generate_chart("BINANCE:BTCUSDT")
            print(f"Chart URL: {chart_url}")
        elif choice == "3":
            print("OpenAI analysis test requires a chart image.")
            print("Please run option 1 or 4 for complete testing.")
        elif choice == "4":
            from demo_test import main as demo_main
            demo_main()
        elif choice == "5":
            return
        else:
            print("Invalid choice.")
        
        input("\nPress Enter to continue...")
        
    except Exception as e:
        print(f"❌ Error in demo/testing: {str(e)}")
        input("Press Enter to continue...")

def show_configuration():
    """Show configuration and settings"""
    print("\n⚙️  CONFIGURATION & SETTINGS")
    print("=" * 40)
    
    # Check API keys
    openai_key = os.getenv('OPENAI_API_KEY')
    chart_key = os.getenv('CHART_IMG_API_KEY')
    
    print("🔑 API Keys Status:")
    print(f"  OpenAI API Key: {'✅ Set' if openai_key else '❌ Not set'}")
    print(f"  Chart-img API Key: {'✅ Set' if chart_key else '❌ Not set'}")
    
    print(f"\n📊 Default Settings:")
    print(f"  Timeframe: {config.DEFAULT_TIMEFRAME} minutes")
    print(f"  Theme: {config.DEFAULT_THEME}")
    print(f"  Chart Size: {config.DEFAULT_WIDTH}x{config.DEFAULT_HEIGHT}")
    print(f"  OpenAI Model: {config.OPENAI_MODEL}")
    
    print(f"\n📈 Available Symbols: {len(config.POPULAR_CRYPTO_SYMBOLS)}")
    print(f"  Top 5: {', '.join(config.POPULAR_CRYPTO_SYMBOLS[:5])}")
    
    print(f"\n📁 File Patterns:")
    print(f"  Analysis: {config.ANALYSIS_FILE_PATTERN}")
    
    if not openai_key or not chart_key:
        print(f"\n⚠️  Setup Required:")
        print(f"  1. Copy .env.example to .env")
        print(f"  2. Add your API keys to .env file")
        print(f"  3. Restart the application")
    
    input("\nPress Enter to continue...")

def show_help():
    """Show help and documentation"""
    print("\n📚 HELP & DOCUMENTATION")
    print("=" * 40)
    
    print("🎯 PURPOSE:")
    print("  This bot analyzes crypto charts on 15-minute timeframe")
    print("  and provides futures trading signals with entry, stop loss,")
    print("  and take profit levels.")
    
    print("\n🔧 SETUP:")
    print("  1. Install Python 3.8+")
    print("  2. Install dependencies: pip install -r requirements.txt")
    print("  3. Get API keys:")
    print("     - OpenAI: https://platform.openai.com/api-keys")
    print("     - Chart-img: https://chart-img.com/")
    print("  4. Configure .env file with your API keys")
    
    print("\n📊 FEATURES:")
    print("  • Single symbol analysis")
    print("  • Batch analysis (multiple symbols)")
    print("  • Scheduled analysis (continuous monitoring)")
    print("  • Save analysis results to files")
    print("  • Risk/Reward ratio calculation")
    print("  • Entry signals with stop loss and take profit")
    
    print("\n💡 TIPS:")
    print("  • Use 15-minute timeframe for best results")
    print("  • Wait 3-5 seconds between API calls to avoid rate limits")
    print("  • Save important analysis results for reference")
    print("  • Check API key credits regularly")
    
    print("\n🆘 TROUBLESHOOTING:")
    print("  • Chart generation fails: Check Chart-img API key")
    print("  • Analysis fails: Check OpenAI API key and credits")
    print("  • Network errors: Check internet connection")
    print("  • Invalid symbols: Use format EXCHANGE:SYMBOL")
    
    print("\n📞 SUPPORT:")
    print("  • Check README.md for detailed instructions")
    print("  • Verify API keys are valid and have credits")
    print("  • Ensure all dependencies are installed")
    
    input("\nPress Enter to continue...")

def main():
    """Main application loop"""
    while True:
        try:
            # Clear screen (works on both Windows and Unix)
            os.system('cls' if os.name == 'nt' else 'clear')
            
            # Show banner and menu
            print_banner()
            print_main_menu()
            
            # Get user choice
            choice = input("Select option (1-7): ").strip()
            
            if choice == "1":
                run_single_analysis()
            elif choice == "2":
                run_batch_analysis()
            elif choice == "3":
                run_scheduled_analysis()
            elif choice == "4":
                run_demo_testing()
            elif choice == "5":
                show_configuration()
            elif choice == "6":
                show_help()
            elif choice == "7":
                print("\n👋 Thank you for using Trading Analyzer Bot!")
                print("🚀 Happy trading and may your profits be high! 📈")
                break
            else:
                print("❌ Invalid choice. Please select 1-7.")
                input("Press Enter to continue...")
                
        except KeyboardInterrupt:
            print("\n\n👋 Application stopped by user. Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Unexpected error: {str(e)}")
            input("Press Enter to continue...")

if __name__ == "__main__":
    main()
