import requests
import json
import base64
import time
import os
from datetime import datetime
from typing import Dict, Any, Optional
from dotenv import load_dotenv
import config

# Load environment variables
load_dotenv()

class TradingAnalyzerBot:
    def __init__(self, openai_api_key: str = None, chart_img_api_key: str = None):
        """
        Initialize Trading Analyzer Bot
        
        Args:
            openai_api_key: OpenAI API key (optional, will use env var if not provided)
            chart_img_api_key: Chart-img.com API key (optional, will use env var if not provided)
        """
        self.openai_api_key = openai_api_key or os.getenv('OPENAI_API_KEY')
        self.chart_img_api_key = chart_img_api_key or os.getenv('CHART_IMG_API_KEY')
        
        if not self.openai_api_key:
            raise ValueError("OpenAI API key not found. Set OPENAI_API_KEY environment variable or pass it directly.")
        if not self.chart_img_api_key:
            raise ValueError("Chart-img API key not found. Set CHART_IMG_API_KEY environment variable or pass it directly.")
            
        self.openai_base_url = "https://api.openai.com/v1"
        self.chart_img_base_url = "https://api.chart-img.com/v2/tradingview/advanced-chart/storage"

        # System prompt untuk analisa trading dari config
        self.system_prompt = config.SYSTEM_PROMPT_TEMPLATE

    def generate_chart(self, symbol: str, timeframe: str = None, theme: str = None) -> Optional[str]:
        """
        Generate chart image using chart-img API

        Args:
            symbol: Trading symbol (e.g., "BINANCE:BTCUSDT")
            timeframe: Chart timeframe (default from config)
            theme: Chart theme (default from config)

        Returns:
            URL of generated chart image or None if failed
        """
        # Use config defaults if not provided
        timeframe = timeframe or config.DEFAULT_TIMEFRAME
        theme = theme or config.DEFAULT_THEME
        headers = {
            "x-api-key": self.chart_img_api_key,
            "content-type": "application/json"
        }
        
        payload = {
            "symbol": symbol,
            "interval": timeframe,
            "theme": theme,
            "studies": config.CHART_STUDIES,
            "width": config.DEFAULT_WIDTH,
            "height": config.DEFAULT_HEIGHT
        }
        
        try:
            print(f"Generating chart for {symbol} on {timeframe}M timeframe...")
            response = requests.post(
                self.chart_img_base_url,
                headers=headers,
                json=payload,
                timeout=config.CHART_IMG_TIMEOUT
            )
            
            if response.status_code == 200:
                result = response.json()
                chart_url = result.get("url")
                print(f"Chart generated successfully: {chart_url}")
                return chart_url
            else:
                print(f"Error generating chart: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"Exception while generating chart: {str(e)}")
            return None

    def download_chart_image(self, chart_url: str) -> Optional[bytes]:
        """
        Download chart image from URL
        
        Args:
            chart_url: URL of the chart image
            
        Returns:
            Image bytes or None if failed
        """
        try:
            print("Downloading chart image...")
            response = requests.get(chart_url, timeout=config.IMAGE_DOWNLOAD_TIMEOUT)
            
            if response.status_code == 200:
                print("Chart image downloaded successfully")
                return response.content
            else:
                print(f"Error downloading chart: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"Exception while downloading chart: {str(e)}")
            return None

    def encode_image_to_base64(self, image_bytes: bytes) -> str:
        """
        Encode image bytes to base64 string
        
        Args:
            image_bytes: Image data in bytes
            
        Returns:
            Base64 encoded string
        """
        return base64.b64encode(image_bytes).decode('utf-8')

    def analyze_chart_with_openai(self, image_base64: str, symbol: str) -> Optional[str]:
        """
        Send chart image to OpenAI for analysis
        
        Args:
            image_base64: Base64 encoded chart image
            symbol: Trading symbol for context
            
        Returns:
            Analysis result or None if failed
        """
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.openai_api_key}"
        }
        
        payload = {
            "model": config.OPENAI_MODEL,
            "messages": [
                {
                    "role": "system",
                    "content": self.system_prompt
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": f"Analisa chart {symbol} berikut ini sesuai dengan instruksi yang telah diberikan:"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{image_base64}",
                                "detail": "high"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": config.OPENAI_MAX_TOKENS,
            "temperature": config.OPENAI_TEMPERATURE
        }
        
        try:
            print("Sending chart to OpenAI for analysis...")
            response = requests.post(
                f"{self.openai_base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                analysis = result["choices"][0]["message"]["content"]
                print("Analysis completed successfully")
                return analysis
            else:
                print(f"Error from OpenAI: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"Exception while analyzing with OpenAI: {str(e)}")
            return None

    def analyze_symbol(self, symbol: str, timeframe: str = None) -> Optional[str]:
        """
        Complete analysis workflow for a trading symbol

        Args:
            symbol: Trading symbol (e.g., "BINANCE:BTCUSDT")
            timeframe: Chart timeframe (default from config)

        Returns:
            Complete analysis result or None if failed
        """
        # Use config default if not provided
        timeframe = timeframe or config.DEFAULT_TIMEFRAME
        print(f"\n{'='*50}")
        print(f"STARTING ANALYSIS FOR {symbol}")
        print(f"Timeframe: {timeframe} minutes")
        print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*50}")
        
        # Step 1: Generate chart
        chart_url = self.generate_chart(symbol, timeframe)
        if not chart_url:
            return "Error: Failed to generate chart"
        
        # Step 2: Download chart image
        image_bytes = self.download_chart_image(chart_url)
        if not image_bytes:
            return "Error: Failed to download chart image"
        
        # Step 3: Encode to base64
        image_base64 = self.encode_image_to_base64(image_bytes)
        
        # Step 4: Analyze with OpenAI
        analysis = self.analyze_chart_with_openai(image_base64, symbol)
        if not analysis:
            return "Error: Failed to analyze chart with OpenAI"
        
        return analysis

    def save_analysis_to_file(self, symbol: str, analysis: str, timeframe: str = None):
        """
        Save analysis result to a text file

        Args:
            symbol: Trading symbol
            analysis: Analysis result
            timeframe: Chart timeframe (default from config)
        """
        # Use config default if not provided
        timeframe = timeframe or config.DEFAULT_TIMEFRAME

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"analysis_{symbol.replace(':', '_')}_{timeframe}M_{timestamp}.txt"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"Trading Analysis Report\n")
                f.write(f"{'='*50}\n")
                f.write(f"Symbol: {symbol}\n")
                f.write(f"Timeframe: {timeframe} minutes\n")
                f.write(f"Analysis Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"{'='*50}\n\n")
                f.write(analysis)
                f.write(f"\n\n{'='*50}\n")
                f.write("End of Analysis Report\n")
            
            print(f"Analysis saved to: {filename}")
            
        except Exception as e:
            print(f"Error saving analysis to file: {str(e)}")

def main():
    """
    Main function to run the trading analyzer bot
    """
    try:
        # Initialize bot (will use environment variables)
        bot = TradingAnalyzerBot()
        
        # Get crypto symbols from config
        crypto_symbols = config.POPULAR_CRYPTO_SYMBOLS
        
        print("🚀 Trading Analyzer Bot Started!")
        print("Specialization: Crypto Futures Analysis (15-Min Timeframe)")
        print("\nAvailable commands:")
        print("1. Type symbol (e.g., BINANCE:BTCUSDT)")
        print("2. Type 'list' to see available symbols")
        print("3. Type 'save' after analysis to save result to file")
        print("4. Type 'quit' to exit")
        
        last_analysis = None
        last_symbol = None
        
        while True:
            try:
                user_input = input("\nEnter command: ").strip()
                
                if user_input.lower() == 'quit':
                    print("Bot stopped. Goodbye!")
                    break
                elif user_input.lower() == 'list':
                    print("\nAvailable crypto symbols:")
                    for i, symbol in enumerate(crypto_symbols, 1):
                        print(f"{i}. {symbol}")
                    continue
                elif user_input.lower() == 'save':
                    if last_analysis and last_symbol:
                        bot.save_analysis_to_file(last_symbol, last_analysis)
                    else:
                        print("No analysis to save. Please run an analysis first.")
                    continue
                elif not user_input:
                    continue
                
                # Analyze the symbol
                result = bot.analyze_symbol(user_input)
                
                if result:
                    print(f"\n{'='*50}")
                    print("ANALYSIS RESULT:")
                    print(f"{'='*50}")
                    print(result)
                    print(f"{'='*50}")
                    
                    # Store for potential saving
                    last_analysis = result
                    last_symbol = user_input
                    
                    print("\nType 'save' to save this analysis to a file.")
                else:
                    print("❌ Analysis failed. Please try again.")
                    
            except KeyboardInterrupt:
                print("\n\nBot stopped by user. Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {str(e)}")
                
    except Exception as e:
        print(f"❌ Failed to initialize bot: {str(e)}")
        print("Please check your API keys in the .env file or environment variables.")

if __name__ == "__main__":
    main()
