import requests
import json
import base64
import time
from datetime import datetime
from typing import Dict, Any, Optional
import os

class TradingAnalyzerBot:
    def __init__(self, openai_api_key: str, chart_img_api_key: str):
        """
        Initialize Trading Analyzer Bot
        
        Args:
            openai_api_key: OpenAI API key
            chart_img_api_key: Chart-img.com API key
        """
        self.openai_api_key = openai_api_key
        self.chart_img_api_key = chart_img_api_key
        self.openai_base_url = "https://api.openai.com/v1"
        self.chart_img_base_url = "https://api.chart-img.com/v2/tradingview/advanced-chart/storage"
        
        # System prompt untuk analisa trading
        self.system_prompt = """
Anda adalah "Futures Maximizer: UltraScalp" - spesialis analisa chart crypto pada timeframe 15 menit.

INSTRUKSI ANALISA:

1. VALIDASI TIMEFRAME:
   - Periksa apakah chart yang dikirim adalah timeframe 15 menit
   - Jika bukan 15M, respon: "Chart ini bukan 15 menit, silakan kirim chart dengan timeframe 15M agar bisa dianalisa."

2. ANALISA CHART (FORMAT TETAP):
   
   **Current Trend**: [Identifikasi tren: bullish/bearish/sideways]
   
   **Resistance & Support**: [Level resistance dan support terdekat secara visual]
   
   **Entry Futures Setup**:
   - Entry: [Harga masuk posisi]
   - Stop Loss: [Titik cut-loss]
   - Take Profit: [Target ambil untung]
   - Posisi: [HANYA Long ATAU Short, tidak boleh dua-duanya]
   - RiskReward: [Rasio 1:2 hingga 1:4]
   
   **Estimasi Waktu Potensi Gerakan**: [Durasi setup bisa berjalan, misal: 1-3 jam]
   
   **Skenario Lanjutan**:
   - If TP: [Jika Take Profit tercapai, setup lanjutan]
   - If SL: [Jika Stop Loss tersentuh, setup cadangan]

3. GAYA BAHASA:
   - Profesional dan mudah dimengerti
   - Langsung ke poin, tanpa bertele-tele
   - Hindari bahasa teknikal berlebihan

LARANGAN:
- Jangan berikan dua opsi setara (long DAN short)
- Jangan analisa chart selain timeframe 15M
- Jangan jawab tanpa mengikuti struktur analisa
"""

    def generate_chart(self, symbol: str, timeframe: str = "15", theme: str = "dark") -> Optional[str]:
        """
        Generate chart image using chart-img API
        
        Args:
            symbol: Trading symbol (e.g., "BINANCE:BTCUSDT")
            timeframe: Chart timeframe (default: "15" for 15 minutes)
            theme: Chart theme (default: "dark")
            
        Returns:
            URL of generated chart image or None if failed
        """
        headers = {
            "x-api-key": self.chart_img_api_key,
            "content-type": "application/json"
        }
        
        payload = {
            "symbol": symbol,
            "interval": timeframe,
            "theme": theme,
            "studies": [
                {"name": "Bollinger Bands"},
                {"name": "Volume"},
                {"name": "Relative Strength Index"},
                {"name": "Moving Average Exponential", "inputs": {"length": 21}},
                {"name": "Moving Average Exponential", "inputs": {"length": 50}}
            ],
            "width": 1200,
            "height": 800
        }
        
        try:
            print(f"Generating chart for {symbol} on {timeframe}M timeframe...")
            response = requests.post(
                self.chart_img_base_url,
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                chart_url = result.get("url")
                print(f"Chart generated successfully: {chart_url}")
                return chart_url
            else:
                print(f"Error generating chart: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"Exception while generating chart: {str(e)}")
            return None

    def download_chart_image(self, chart_url: str) -> Optional[bytes]:
        """
        Download chart image from URL
        
        Args:
            chart_url: URL of the chart image
            
        Returns:
            Image bytes or None if failed
        """
        try:
            print("Downloading chart image...")
            response = requests.get(chart_url, timeout=30)
            
            if response.status_code == 200:
                print("Chart image downloaded successfully")
                return response.content
            else:
                print(f"Error downloading chart: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"Exception while downloading chart: {str(e)}")
            return None

    def encode_image_to_base64(self, image_bytes: bytes) -> str:
        """
        Encode image bytes to base64 string
        
        Args:
            image_bytes: Image data in bytes
            
        Returns:
            Base64 encoded string
        """
        return base64.b64encode(image_bytes).decode('utf-8')

    def analyze_chart_with_openai(self, image_base64: str, symbol: str) -> Optional[str]:
        """
        Send chart image to OpenAI for analysis
        
        Args:
            image_base64: Base64 encoded chart image
            symbol: Trading symbol for context
            
        Returns:
            Analysis result or None if failed
        """
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.openai_api_key}"
        }
        
        payload = {
            "model": "gpt-4o",  # Latest and best model for vision
            "messages": [
                {
                    "role": "system",
                    "content": self.system_prompt
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": f"Analisa chart {symbol} berikut ini sesuai dengan instruksi yang telah diberikan:"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{image_base64}",
                                "detail": "high"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 1500,
            "temperature": 0.3
        }
        
        try:
            print("Sending chart to OpenAI for analysis...")
            response = requests.post(
                f"{self.openai_base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                analysis = result["choices"][0]["message"]["content"]
                print("Analysis completed successfully")
                return analysis
            else:
                print(f"Error from OpenAI: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"Exception while analyzing with OpenAI: {str(e)}")
            return None

    def analyze_symbol(self, symbol: str, timeframe: str = "15") -> Optional[str]:
        """
        Complete analysis workflow for a trading symbol
        
        Args:
            symbol: Trading symbol (e.g., "BINANCE:BTCUSDT")
            timeframe: Chart timeframe (default: "15")
            
        Returns:
            Complete analysis result or None if failed
        """
        print(f"\n{'='*50}")
        print(f"STARTING ANALYSIS FOR {symbol}")
        print(f"Timeframe: {timeframe} minutes")
        print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*50}")
        
        # Step 1: Generate chart
        chart_url = self.generate_chart(symbol, timeframe)
        if not chart_url:
            return "Error: Failed to generate chart"
        
        # Step 2: Download chart image
        image_bytes = self.download_chart_image(chart_url)
        if not image_bytes:
            return "Error: Failed to download chart image"
        
        # Step 3: Encode to base64
        image_base64 = self.encode_image_to_base64(image_bytes)
        
        # Step 4: Analyze with OpenAI
        analysis = self.analyze_chart_with_openai(image_base64, symbol)
        if not analysis:
            return "Error: Failed to analyze chart with OpenAI"
        
        return analysis

def main():
    """
    Main function to run the trading analyzer bot
    """
    # API Keys
    OPENAI_API_KEY = "***************************************************"
    CHART_IMG_API_KEY = "of8uldEeWa2bVpOuhdqAI90JzsfcIYCP8BJSgMsw"
    
    # Initialize bot
    bot = TradingAnalyzerBot(OPENAI_API_KEY, CHART_IMG_API_KEY)
    
    # Example symbols for crypto analysis
    crypto_symbols = [
        "BINANCE:BTCUSDT",
        "BINANCE:ETHUSDT", 
        "BINANCE:ADAUSDT",
        "BINANCE:SOLUSDT",
        "BINANCE:DOTUSDT"
    ]
    
    print("🚀 Trading Analyzer Bot Started!")
    print("Specialization: Crypto Futures Analysis (15-Min Timeframe)")
    print("\nAvailable commands:")
    print("1. Type symbol (e.g., BINANCE:BTCUSDT)")
    print("2. Type 'list' to see available symbols")
    print("3. Type 'quit' to exit")
    
    while True:
        try:
            user_input = input("\nEnter symbol to analyze: ").strip()
            
            if user_input.lower() == 'quit':
                print("Bot stopped. Goodbye!")
                break
            elif user_input.lower() == 'list':
                print("\nAvailable crypto symbols:")
                for i, symbol in enumerate(crypto_symbols, 1):
                    print(f"{i}. {symbol}")
                continue
            elif not user_input:
                continue
            
            # Analyze the symbol
            result = bot.analyze_symbol(user_input)
            
            if result:
                print(f"\n{'='*50}")
                print("ANALYSIS RESULT:")
                print(f"{'='*50}")
                print(result)
                print(f"{'='*50}")
            else:
                print("❌ Analysis failed. Please try again.")
                
        except KeyboardInterrupt:
            print("\n\nBot stopped by user. Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
