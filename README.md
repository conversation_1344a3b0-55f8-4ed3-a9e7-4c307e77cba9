# Trading Analyzer Bot - Futures Maximizer

Bot analisa trading crypto yang mengintegrasikan Chart-img API dengan OpenAI untuk analisa chart pada timeframe 15 menit. Spesialisasi UltraScalp dengan AI-powered chart reading.

## 🚀 Fitur Utama

- 🔍 **Single Symbol Analysis**: <PERSON><PERSON><PERSON> mendalam untuk satu symbol crypto
- 📊 **Batch Analysis**: Ana<PERSON>a multiple symbols sekaligus
- 🕐 **Scheduled Analysis**: Monitoring otomatis dengan interval waktu
- 🤖 **AI-Powered**: Menggunakan GPT-4o (model terbaik OpenAI) untuk analisa chart
- 📈 **Real-time Charts**: Mengambil chart crypto secara real-time dari TradingView
- 💡 **Structured Analysis**: Entry, Stop Loss, Take Profit, Risk/Reward ratio
- 📁 **Report Generation**: Menyimpan hasil analisa ke file untuk referensi
- ⚙️ **Configurable**: Pengaturan yang dapat disesuaikan via config file

## Struktur Analisa

Bot akan memberikan analisa dengan format berikut:

1. **Current Trend**: Identifikasi tren (bullish/bearish/sideways)
2. **Resistance & Support**: Level resistance dan support terdekat
3. **Entry Futures Setup**:
   - Entry: Harga masuk posisi
   - Stop Loss: Titik cut-loss
   - Take Profit: Target ambil untung
   - Posisi: Long ATAU Short (tidak keduanya)
   - RiskReward: Rasio 1:2 hingga 1:4
4. **Estimasi Waktu**: Durasi potensi gerakan
5. **Skenario Lanjutan**: Setup jika TP/SL tercapai

## 📁 File Structure

```
tradingai_gpt/
├── main_launcher.py          # 🚀 Main application launcher (START HERE)
├── trading_analyzer_bot.py   # Basic version with hardcoded API keys
├── trading_analyzer_secure.py # Secure version with environment variables
├── batch_analyzer.py         # Batch analysis for multiple symbols
├── scheduled_analyzer.py     # Scheduled analysis with intervals
├── demo_test.py             # Demo and testing functionality
├── config.py                # Configuration settings
├── requirements.txt         # Python dependencies
├── .env.example            # Environment variables template
├── run_bot.bat             # Windows batch launcher
└── README.md               # This documentation
```

## 🛠️ Instalasi

1. **Download semua file ke folder project Anda**

2. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Setup API Keys:**

   **Opsi A: Environment Variables (Recommended)**
   ```bash
   # Copy template file
   copy .env.example .env

   # Edit .env file dan masukkan API keys Anda:
   OPENAI_API_KEY=***************************************************
   CHART_IMG_API_KEY=of8uldEeWa2bVpOuhdqAI90JzsfcIYCP8BJSgMsw
   ```

   **Opsi B: Hardcoded (sudah diset di trading_analyzer_bot.py)**

## 🚀 Cara Penggunaan

### Quick Start (Recommended):

**Windows:**
```bash
run_bot.bat
```
Pilih option 1 untuk Main Launcher yang menyediakan akses ke semua fitur.

**Manual:**
```bash
python main_launcher.py
```

### Fitur-Fitur Available:

#### 1. 🔍 Single Symbol Analysis
- Analisa satu symbol crypto secara mendalam
- Input manual atau pilih dari daftar popular symbols
- Hasil analisa lengkap dengan entry, SL, TP

#### 2. 📊 Batch Analysis
- Analisa multiple symbols sekaligus
- Pilihan: popular symbols, custom list, atau top N symbols
- Generate comprehensive batch report

#### 3. 🕐 Scheduled Analysis
- Monitoring otomatis dengan interval waktu
- Continuous analysis setiap X jam
- Auto-save reports dengan timestamp

#### 4. 🧪 Demo & Testing
- Test functionality sebelum penggunaan real
- Verify API keys dan koneksi
- Debug mode untuk troubleshooting

### Alternative Usage:

**Individual Components:**
```bash
python trading_analyzer_secure.py    # Secure single analysis
python batch_analyzer.py            # Batch analysis only
python scheduled_analyzer.py        # Scheduled analysis only
python demo_test.py                 # Testing only
```

### Contoh Penggunaan:

```
🚀 Trading Analyzer Bot Started!
Specialization: Crypto Futures Analysis (15-Min Timeframe)

Available commands:
1. Type symbol (e.g., BINANCE:BTCUSDT)
2. Type 'list' to see available symbols
3. Type 'save' after analysis to save result to file
4. Type 'quit' to exit

Enter command: BINANCE:BTCUSDT

==================================================
STARTING ANALYSIS FOR BINANCE:BTCUSDT
Timeframe: 15 minutes
Time: 2024-01-20 14:30:15
==================================================
Generating chart for BINANCE:BTCUSDT on 15M timeframe...
Chart generated successfully: https://r2.chart-img.com/...
Downloading chart image...
Chart image downloaded successfully
Sending chart to OpenAI for analysis...
Analysis completed successfully

==================================================
ANALYSIS RESULT:
==================================================
**Current Trend**: Bullish

**Resistance & Support**: 
- Resistance: $43,500
- Support: $42,800

**Entry Futures Setup**:
- Entry: $43,100
- Stop Loss: $42,750
- Take Profit: $43,800
- Posisi: Long
- RiskReward: 1:2

**Estimasi Waktu Potensi Gerakan**: 2-4 jam ke depan

**Skenario Lanjutan**:
- If TP: Monitor level $44,200 untuk entry ulang
- If SL: Wait for retest support $42,500 untuk entry baru
==================================================

Type 'save' to save this analysis to a file.

Enter command: save
Analysis saved to: analysis_BINANCE_BTCUSDT_15M_20240120_143025.txt
```

## Symbols yang Didukung

Bot mendukung semua symbol crypto dari Binance:
- BINANCE:BTCUSDT
- BINANCE:ETHUSDT
- BINANCE:ADAUSDT
- BINANCE:SOLUSDT
- BINANCE:DOTUSDT
- BINANCE:LINKUSDT
- BINANCE:AVAXUSDT
- BINANCE:MATICUSDT
- Dan symbol Binance lainnya

## Technical Indicators

Chart yang dianalisa menggunakan indikator:
- Bollinger Bands
- Volume
- RSI (Relative Strength Index)
- EMA 21 & EMA 50
- MACD

## API Keys

### OpenAI API Key
- Model yang digunakan: `gpt-4o` (model terbaik untuk vision)
- Pastikan account OpenAI Anda memiliki akses ke GPT-4o

### Chart-img API Key
- Service: https://chart-img.com/
- Endpoint: TradingView Advanced Chart
- Resolusi: 1200x800 pixels

## Troubleshooting

### Error: API Key tidak valid
- Pastikan API key OpenAI dan Chart-img sudah benar
- Cek apakah account masih aktif dan memiliki credit

### Error: Chart generation failed
- Cek koneksi internet
- Pastikan symbol yang dimasukkan valid (format: EXCHANGE:SYMBOL)

### Error: OpenAI analysis failed
- Pastikan account OpenAI memiliki akses ke GPT-4o
- Cek apakah masih ada credit di account OpenAI

## File Output

Hasil analisa dapat disimpan dengan format:
```
analysis_BINANCE_BTCUSDT_15M_20240120_143025.txt
```

Format: `analysis_{SYMBOL}_{TIMEFRAME}M_{TIMESTAMP}.txt`

## Keamanan

- Gunakan `trading_analyzer_secure.py` untuk keamanan API key yang lebih baik
- Jangan commit file `.env` ke repository
- Simpan API key di environment variables untuk production

## Support

Jika ada masalah atau pertanyaan, pastikan:
1. Dependencies sudah terinstall dengan benar
2. API keys sudah valid dan aktif
3. Koneksi internet stabil
4. Format symbol sudah benar

## Disclaimer

Bot ini hanya untuk tujuan analisa dan edukasi. Tidak ada jaminan keakuratan prediksi. Selalu lakukan riset sendiri sebelum melakukan trading.
