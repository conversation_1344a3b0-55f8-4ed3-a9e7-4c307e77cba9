# 🚀 Quick Start Guide - Trading Analyzer Bot

## ⚡ Super Quick Start (1 Minute Setup)

1. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the bot:**
   ```bash
   python main_launcher.py
   ```
   atau di Windows:
   ```bash
   run_bot.bat
   ```

3. **Pilih option 1** untuk Single Symbol Analysis

4. **Test dengan BTCUSDT:**
   - Ketik `1` untuk memilih BINANCE:BTCUSDT
   - Tekan Enter untuk timeframe default (15 menit)
   - Tunggu analisa selesai

**Done! 🎉** Bot akan memberikan analisa trading lengkap.

---

## 📋 Menu Utama

Ketika menjalankan `main_launcher.py`, Anda akan melihat menu:

```
📋 MAIN MENU
========================================
1. 🔍 Single Symbol Analysis
2. 📊 Batch Analysis (Multiple Symbols)  
3. 🕐 Scheduled Analysis (Continuous)
4. 🧪 Demo & Testing
5. ⚙️  Configuration & Settings
6. 📚 Help & Documentation
7. 🚪 Exit
========================================
```

## 🔍 Option 1: Single Symbol Analysis

**Paling cocok untuk:** Analisa cepat satu symbol

**Cara pakai:**
1. Pilih option 1
2. Pilih symbol dari list (1-10) atau ketik manual
3. Set timeframe (default: 15 menit)
4. Tunggu hasil analisa
5. Save ke file jika diperlukan

**Contoh output:**
```
**Current Trend**: Bullish
**Resistance & Support**: 
- Resistance: $43,500
- Support: $42,800
**Entry Futures Setup**:
- Entry: $43,100
- Stop Loss: $42,750  
- Take Profit: $43,800
- Posisi: Long
- RiskReward: 1:2
```

## 📊 Option 2: Batch Analysis

**Paling cocok untuk:** Scan multiple symbols sekaligus

**Cara pakai:**
1. Pilih option 2
2. Pilih jenis analisa:
   - Popular crypto symbols (15 symbols)
   - Custom symbols (input manual)
   - Quick analysis (top 5)
3. Set delay antar request (default: 5 detik)
4. Tunggu batch analysis selesai
5. Save batch report

**Keuntungan:**
- Analisa 5-15 symbols sekaligus
- Auto-save individual analysis
- Generate comprehensive batch report
- Success rate tracking

## 🕐 Option 3: Scheduled Analysis

**Paling cocok untuk:** Monitoring continuous

**Cara pakai:**
1. Pilih option 3
2. Buat watchlist (pilih symbols)
3. Set interval (default: 4 jam)
4. Set timeframe (default: 15 menit)
5. Confirm dan start scheduler

**Fitur:**
- Analisa otomatis setiap X jam
- Auto-save reports dengan timestamp
- Continuous monitoring
- Stop dengan Ctrl+C

## 🧪 Option 4: Demo & Testing

**Paling cocok untuk:** Verify setup dan troubleshooting

**Test yang tersedia:**
1. Basic functionality test
2. Chart generation only
3. OpenAI analysis only  
4. Full demo

**Gunakan ini jika:**
- Pertama kali setup
- Ada masalah dengan API keys
- Ingin verify koneksi

## ⚙️ Option 5: Configuration

**Melihat:**
- Status API keys
- Default settings
- Available symbols
- File patterns

**Setup API Keys:**
- OpenAI: https://platform.openai.com/api-keys
- Chart-img: https://chart-img.com/

## 📚 Option 6: Help

Dokumentasi lengkap dalam aplikasi.

---

## 🎯 Use Cases

### Scenario 1: Quick Check
**Goal:** Cek signal BTCUSDT sekarang
**Action:** Option 1 → pilih BTCUSDT → analisa

### Scenario 2: Market Scan  
**Goal:** Scan top crypto untuk opportunities
**Action:** Option 2 → Quick analysis (top 5) → review batch report

### Scenario 3: Continuous Monitoring
**Goal:** Monitor watchlist setiap 4 jam
**Action:** Option 3 → buat watchlist → set 4 jam interval → start

### Scenario 4: Troubleshooting
**Goal:** Bot tidak jalan dengan benar
**Action:** Option 4 → run full demo → check error messages

---

## ⚠️ Troubleshooting

### Chart generation failed
- ✅ Check Chart-img API key di .env file
- ✅ Check internet connection
- ✅ Verify symbol format (EXCHANGE:SYMBOL)

### OpenAI analysis failed  
- ✅ Check OpenAI API key di .env file
- ✅ Check OpenAI account credits
- ✅ Verify GPT-4o access

### Network errors
- ✅ Check internet connection
- ✅ Try again after few minutes
- ✅ Check if APIs are down

---

## 💡 Pro Tips

1. **Start with Option 4** untuk verify setup
2. **Use 15M timeframe** untuk hasil terbaik
3. **Save important analysis** untuk referensi
4. **Monitor API credits** regularly
5. **Use batch analysis** untuk market scanning
6. **Use scheduled analysis** untuk monitoring

---

## 🆘 Need Help?

1. **Run Option 6** untuk help dalam aplikasi
2. **Check README.md** untuk dokumentasi lengkap
3. **Run demo test** untuk troubleshooting
4. **Verify API keys** di configuration menu

**Happy Trading! 📈🚀**
