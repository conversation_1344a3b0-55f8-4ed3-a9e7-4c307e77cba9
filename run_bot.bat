@echo off
echo ========================================
echo Trading Analyzer Bot Launcher
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

REM Check if requirements are installed
echo Checking dependencies...
pip show requests >nul 2>&1
if errorlevel 1 (
    echo Installing dependencies...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)

echo.
echo Select version to run:
echo 1. Main Launcher (Recommended - All Features)
echo 2. Secure version (uses .env file)
echo 3. Standard version (hardcoded API keys)
echo 4. Batch analyzer
echo 5. Scheduled analyzer
echo 6. Demo test
echo 7. Exit
echo.

set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" (
    echo.
    echo Starting Main Launcher...
    echo This provides access to all bot features
    echo.
    python main_launcher.py
) else if "%choice%"=="2" (
    echo.
    echo Starting secure version...
    echo Make sure you have configured .env file with your API keys
    echo.
    python trading_analyzer_secure.py
) else if "%choice%"=="3" (
    echo.
    echo Starting standard version...
    echo.
    python trading_analyzer_bot.py
) else if "%choice%"=="4" (
    echo.
    echo Starting batch analyzer...
    echo.
    python batch_analyzer.py
) else if "%choice%"=="5" (
    echo.
    echo Starting scheduled analyzer...
    echo.
    python scheduled_analyzer.py
) else if "%choice%"=="6" (
    echo.
    echo Running demo test...
    echo.
    python demo_test.py
) else if "%choice%"=="7" (
    echo Goodbye!
    exit /b 0
) else (
    echo Invalid choice. Please run the script again.
)

echo.
pause
