#!/usr/bin/env python3
"""
Batch Trading Analyzer - Analyze multiple crypto symbols at once
"""

import time
import os
from datetime import datetime
from typing import List, Dict
from trading_analyzer_secure import TradingAnalyzerBot
import config

class BatchTradingAnalyzer:
    def __init__(self, openai_api_key: str = None, chart_img_api_key: str = None):
        """
        Initialize Batch Trading Analyzer
        
        Args:
            openai_api_key: OpenAI API key (optional, will use env var if not provided)
            chart_img_api_key: Chart-img.com API key (optional, will use env var if not provided)
        """
        self.bot = TradingAnalyzerBot(openai_api_key, chart_img_api_key)
        self.results = {}
        
    def analyze_multiple_symbols(self, symbols: List[str], timeframe: str = None, delay: int = 5) -> Dict[str, str]:
        """
        Analyze multiple symbols with delay between requests
        
        Args:
            symbols: List of trading symbols
            timeframe: Chart timeframe (default from config)
            delay: Delay in seconds between requests (to avoid rate limits)
            
        Returns:
            Dictionary with symbol as key and analysis result as value
        """
        timeframe = timeframe or config.DEFAULT_TIMEFRAME
        total_symbols = len(symbols)
        
        print(f"🚀 Starting batch analysis for {total_symbols} symbols")
        print(f"Timeframe: {timeframe} minutes")
        print(f"Delay between requests: {delay} seconds")
        print("=" * 60)
        
        for i, symbol in enumerate(symbols, 1):
            print(f"\n[{i}/{total_symbols}] Analyzing {symbol}...")
            
            try:
                # Analyze symbol
                result = self.bot.analyze_symbol(symbol, timeframe)
                
                if result and not result.startswith("Error:"):
                    self.results[symbol] = result
                    print(f"✅ {symbol} analysis completed")
                    
                    # Save individual analysis
                    self.bot.save_analysis_to_file(symbol, result, timeframe)
                    
                else:
                    error_msg = result or "Unknown error"
                    self.results[symbol] = f"❌ Analysis failed: {error_msg}"
                    print(f"❌ {symbol} analysis failed: {error_msg}")
                
            except Exception as e:
                error_msg = f"Exception: {str(e)}"
                self.results[symbol] = f"❌ Analysis failed: {error_msg}"
                print(f"❌ {symbol} analysis failed: {error_msg}")
            
            # Add delay between requests (except for last symbol)
            if i < total_symbols:
                print(f"Waiting {delay} seconds before next analysis...")
                time.sleep(delay)
        
        print("\n" + "=" * 60)
        print("🎉 Batch analysis completed!")
        
        return self.results
    
    def generate_batch_report(self, symbols: List[str], timeframe: str = None) -> str:
        """
        Generate a comprehensive batch analysis report
        
        Args:
            symbols: List of analyzed symbols
            timeframe: Chart timeframe
            
        Returns:
            Formatted batch report
        """
        timeframe = timeframe or config.DEFAULT_TIMEFRAME
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        report = f"""
BATCH TRADING ANALYSIS REPORT
{'=' * 60}
Analysis Time: {timestamp}
Timeframe: {timeframe} minutes
Total Symbols Analyzed: {len(symbols)}
{'=' * 60}

"""
        
        successful_analyses = 0
        failed_analyses = 0
        
        for symbol in symbols:
            result = self.results.get(symbol, "No analysis available")
            
            report += f"\n{'─' * 40}\n"
            report += f"SYMBOL: {symbol}\n"
            report += f"{'─' * 40}\n"
            
            if result.startswith("❌"):
                failed_analyses += 1
                report += result + "\n"
            else:
                successful_analyses += 1
                report += result + "\n"
        
        # Summary
        report += f"\n{'=' * 60}\n"
        report += f"BATCH ANALYSIS SUMMARY\n"
        report += f"{'=' * 60}\n"
        report += f"✅ Successful Analyses: {successful_analyses}\n"
        report += f"❌ Failed Analyses: {failed_analyses}\n"
        report += f"📊 Success Rate: {(successful_analyses/len(symbols)*100):.1f}%\n"
        report += f"{'=' * 60}\n"
        
        return report
    
    def save_batch_report(self, symbols: List[str], timeframe: str = None) -> str:
        """
        Save batch analysis report to file
        
        Args:
            symbols: List of analyzed symbols
            timeframe: Chart timeframe
            
        Returns:
            Filename of saved report
        """
        timeframe = timeframe or config.DEFAULT_TIMEFRAME
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"batch_analysis_{timeframe}M_{timestamp}.txt"
        
        report = self.generate_batch_report(symbols, timeframe)
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report)
            
            print(f"📁 Batch report saved to: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ Error saving batch report: {str(e)}")
            return None
    
    def print_summary(self):
        """Print a summary of batch analysis results"""
        if not self.results:
            print("No analysis results available.")
            return
        
        successful = sum(1 for result in self.results.values() if not result.startswith("❌"))
        failed = len(self.results) - successful
        
        print(f"\n📊 BATCH ANALYSIS SUMMARY")
        print(f"{'=' * 40}")
        print(f"Total Symbols: {len(self.results)}")
        print(f"✅ Successful: {successful}")
        print(f"❌ Failed: {failed}")
        print(f"📈 Success Rate: {(successful/len(self.results)*100):.1f}%")
        print(f"{'=' * 40}")
        
        if failed > 0:
            print(f"\n❌ Failed Symbols:")
            for symbol, result in self.results.items():
                if result.startswith("❌"):
                    print(f"  - {symbol}: {result}")

def main():
    """Main function for batch analysis"""
    try:
        # Initialize batch analyzer
        analyzer = BatchTradingAnalyzer()
        
        print("🚀 Batch Trading Analyzer")
        print("=" * 50)
        print("Options:")
        print("1. Analyze popular crypto symbols")
        print("2. Analyze custom symbols")
        print("3. Quick analysis (top 5 symbols)")
        print("4. Exit")
        
        choice = input("\nSelect option (1-4): ").strip()
        
        if choice == "1":
            # Analyze all popular symbols
            symbols = config.POPULAR_CRYPTO_SYMBOLS
            print(f"\nAnalyzing {len(symbols)} popular crypto symbols...")
            
        elif choice == "2":
            # Custom symbols
            print("\nEnter symbols separated by commas (e.g., BINANCE:BTCUSDT,BINANCE:ETHUSDT):")
            symbols_input = input("Symbols: ").strip()
            symbols = [s.strip() for s in symbols_input.split(",") if s.strip()]
            
            if not symbols:
                print("No valid symbols entered. Exiting.")
                return
                
        elif choice == "3":
            # Quick analysis - top 5
            symbols = config.POPULAR_CRYPTO_SYMBOLS[:5]
            print(f"\nQuick analysis of top 5 symbols...")
            
        elif choice == "4":
            print("Goodbye!")
            return
            
        else:
            print("Invalid choice. Exiting.")
            return
        
        # Get delay setting
        delay_input = input(f"\nDelay between requests in seconds (default: 5): ").strip()
        delay = int(delay_input) if delay_input.isdigit() else 5
        
        # Start batch analysis
        print(f"\nStarting batch analysis...")
        results = analyzer.analyze_multiple_symbols(symbols, delay=delay)
        
        # Print summary
        analyzer.print_summary()
        
        # Ask to save report
        save_report = input("\nSave batch report to file? (y/n): ").strip().lower()
        if save_report in ['y', 'yes']:
            analyzer.save_batch_report(symbols)
        
        print("\n🎉 Batch analysis completed!")
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Batch analysis stopped by user.")
    except Exception as e:
        print(f"\n❌ Error in batch analysis: {str(e)}")

if __name__ == "__main__":
    main()
